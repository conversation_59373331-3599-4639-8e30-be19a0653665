/* You can add global styles to this file, and also import other style files */

$gutter: 1rem; //for primeflex grid system
@import 'assets/layout/styles/layout/layout.scss';

/* PrimeNG */
@import '../node_modules/primeng/resources/primeng.min.css';
@import '../node_modules/primeflex/primeflex.scss';
@import '../node_modules/primeicons/primeicons.css';

/* ===== NISSHIN VIETNAM RED THEME VARIABLES ===== */
:root {
    /* Nisshin Red Color Palette */
    --nisshin-red-primary: #dc2626;
    --nisshin-red-secondary: #b91c1c;
    --nisshin-red-light: #fef2f2;
    --nisshin-red-dark: #991b1b;
    --nisshin-red-accent: #f87171;

    /* Spacing System */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Border Radius System */
    --radius-sm: 6px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
}

/* ===== LIGHT THEME VARIABLES ===== */
.layout-theme-light {
    /* Enhanced Color System - Light */
    --surface-card-enhanced: #ffffff;
    --surface-hover-enhanced: #f8fafc;
    --border-color-enhanced: #e2e8f0;
    --text-primary-enhanced: #1e293b;
    --text-secondary-enhanced: #64748b;
    --background-enhanced: #f8fafc;

    /* Shadow System - Light */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* ===== DARK THEME VARIABLES ===== */
.layout-theme-dark {
    /* Enhanced Color System - Dark */
    --surface-card-enhanced: #1f2937;
    --surface-hover-enhanced: #374151;
    --border-color-enhanced: #4b5563;
    --text-primary-enhanced: rgba(255, 255, 255, 0.87);
    --text-secondary-enhanced: rgba(255, 255, 255, 0.6);
    --background-enhanced: #111827;

    /* Shadow System - Dark */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.5), 0 10px 10px -5px rgba(0, 0, 0, 0.4);
}

/* ===== GLOBAL ENHANCEMENTS ===== */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary-enhanced);
    background-color: var(--background-enhanced);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* ===== ENHANCED CARD STYLING ===== */
.card {
    background: var(--surface-card-enhanced);
    border: 1px solid var(--border-color-enhanced);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease;

    &:hover {
        box-shadow: var(--shadow-md);
        transform: translateY(-1px);
    }
}

/* ===== CUSTOM COMPONENTS ===== */
/*  CUSTOM GROUP BUTTON EDIT IN TABLE */
.custom-group-button-edit {
    .p-button.p-button-sm {
        padding: 6px 12px;
        border-radius: var(--radius-sm);
        font-weight: 500;
        transition: all 0.2s ease;

        &:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-sm);
        }
    }
}

/*  CUSTOM AVATAR */
.custom-avatar {
    .p-avatar {
        background: linear-gradient(135deg, var(--nisshin-red-primary), var(--nisshin-red-secondary));
        color: white;
        border: 2px solid white;
        box-shadow: var(--shadow-md);
        transition: all 0.2s ease;

        &:hover {
            transform: scale(1.05);
            box-shadow: var(--shadow-lg);
        }
    }
}

/* ===== PRIMENG COMPONENT ENHANCEMENTS ===== */

/* Enhanced Table Styling */
.p-datatable {
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color-enhanced);

    .p-datatable-header {
        background: linear-gradient(135deg, var(--nisshin-red-light), #ffffff);
        border-bottom: 2px solid var(--nisshin-red-primary);
        padding: var(--spacing-lg);

        h5 {
            color: var(--nisshin-red-dark);
            font-weight: 600;
            margin: 0;
        }
    }

    .p-datatable-thead > tr > th {
        background: var(--surface-hover-enhanced);
        color: var(--text-primary-enhanced);
        font-weight: 600;
        padding: var(--spacing-md) var(--spacing-lg);
        border-bottom: 2px solid var(--border-color-enhanced);
    }

    .p-datatable-tbody > tr {
        transition: all 0.2s ease;

        &:hover {
            background: var(--surface-hover-enhanced);
            transform: translateX(2px);
        }

        > td {
            padding: var(--spacing-md) var(--spacing-lg);
            border-bottom: 1px solid var(--border-color-enhanced);
        }
    }
}

/* Enhanced Button Styling */
.p-button {
    border-radius: var(--radius-md);
    font-weight: 500;
    transition: all 0.2s ease;

    &.p-button-danger {
        background: linear-gradient(135deg, var(--nisshin-red-primary), var(--nisshin-red-secondary));
        border-color: var(--nisshin-red-primary);

        &:hover {
            background: linear-gradient(135deg, var(--nisshin-red-secondary), var(--nisshin-red-dark));
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }
    }

    &.p-button-outlined {
        border: 2px solid var(--nisshin-red-primary);
        color: var(--nisshin-red-primary);

        &:hover {
            background: var(--nisshin-red-primary);
            color: white;
            transform: translateY(-1px);
        }
    }
}

/* Enhanced Dialog Styling */
.p-dialog {
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    border: none;

    .p-dialog-header {
        background: linear-gradient(135deg, var(--nisshin-red-primary), var(--nisshin-red-secondary));
        color: white;
        border-radius: var(--radius-xl) var(--radius-xl) 0 0;
        padding: var(--spacing-lg);

        .p-dialog-title {
            font-weight: 600;
        }

        .p-dialog-header-icon {
            color: white;

            &:hover {
                background: rgba(255, 255, 255, 0.1);
            }
        }
    }

    .p-dialog-content {
        padding: var(--spacing-xl);
    }
}

/* Enhanced Input Styling */
.p-inputtext, .p-dropdown, .p-calendar {
    border-radius: var(--radius-md);
    border: 2px solid var(--border-color-enhanced);
    transition: all 0.2s ease;

    &:focus {
        border-color: var(--nisshin-red-primary);
        box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
    }
}

/* Enhanced Toast Styling */
.p-toast {
    .p-toast-message {
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        border: none;

        &.p-toast-message-success {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        &.p-toast-message-error {
            background: linear-gradient(135deg, var(--nisshin-red-primary), var(--nisshin-red-secondary));
        }

        &.p-toast-message-warn {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }
    }
}

/* ===== RESPONSIVE DESIGN ENHANCEMENTS ===== */
@media (max-width: 768px) {
    .p-datatable {
        .p-datatable-header {
            padding: var(--spacing-md);

            h5 {
                font-size: 1.1rem;
            }
        }

        .p-datatable-thead > tr > th,
        .p-datatable-tbody > tr > td {
            padding: var(--spacing-sm) var(--spacing-md);
            font-size: 0.875rem;
        }
    }

    .p-dialog {
        margin: var(--spacing-md);
        width: calc(100% - 2rem) !important;

        .p-dialog-content {
            padding: var(--spacing-lg);
        }
    }

    .custom-group-button-edit {
        .p-button.p-button-sm {
            padding: 4px 8px;
            font-size: 0.75rem;
        }
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .p-datatable {
        .p-datatable-header {
            padding: var(--spacing-lg);
        }

        .p-datatable-thead > tr > th,
        .p-datatable-tbody > tr > td {
            padding: var(--spacing-md);
        }
    }
}

@media (min-width: 1025px) {
    .p-datatable {
        .p-datatable-tbody > tr:hover {
            transform: translateX(4px);
        }
    }

    .card:hover {
        transform: translateY(-2px);
    }
}

/* ===== UTILITY CLASSES ===== */
.text-nisshin-red {
    color: var(--nisshin-red-primary) !important;
}

.bg-nisshin-red {
    background: var(--nisshin-red-primary) !important;
}

.bg-nisshin-gradient {
    background: linear-gradient(135deg, var(--nisshin-red-primary), var(--nisshin-red-secondary)) !important;
}

.border-nisshin-red {
    border-color: var(--nisshin-red-primary) !important;
}

.shadow-enhanced {
    box-shadow: var(--shadow-md) !important;
}

.rounded-enhanced {
    border-radius: var(--radius-lg) !important;
}

.transition-smooth {
    transition: all 0.2s ease !important;
}

/* Spacing utilities */
.p-xs { padding: var(--spacing-xs) !important; }
.p-sm { padding: var(--spacing-sm) !important; }
.p-md { padding: var(--spacing-md) !important; }
.p-lg { padding: var(--spacing-lg) !important; }
.p-xl { padding: var(--spacing-xl) !important; }

.m-xs { margin: var(--spacing-xs) !important; }
.m-sm { margin: var(--spacing-sm) !important; }
.m-md { margin: var(--spacing-md) !important; }
.m-lg { margin: var(--spacing-lg) !important; }
.m-xl { margin: var(--spacing-xl) !important; }

/* ===== ENHANCED ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.3s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

/* ===== CURVED DIVIDERS (BRIDGE STYLE) ===== */
.divider-curved {
    position: relative;
    height: 2px;
    background: linear-gradient(90deg,
        transparent 0%,
        var(--nisshin-red-primary, #dc2626) 20%,
        var(--nisshin-red-accent, #f87171) 50%,
        var(--nisshin-red-primary, #dc2626) 80%,
        transparent 100%);
    border-radius: 50px;
    margin: var(--spacing-lg, 1.5rem) 0;

    &::before {
        content: '';
        position: absolute;
        top: -1px;
        left: 10%;
        right: 10%;
        height: 4px;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(220, 38, 38, 0.3) 50%,
            transparent 100%);
        border-radius: 50px;
        filter: blur(1px);
    }
}

.divider-curved-small {
    position: relative;
    height: 1px;
    background: linear-gradient(90deg,
        transparent 0%,
        var(--nisshin-red-primary, #dc2626) 30%,
        var(--nisshin-red-primary, #dc2626) 70%,
        transparent 100%);
    border-radius: 25px;
    margin: var(--spacing-md, 1rem) 0;
    width: 60%;
    margin-left: auto;
    margin-right: auto;
}

/* Section dividers with subtle curves */
.section-divider {
    position: relative;
    margin: var(--spacing-2xl, 3rem) 0;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg,
            transparent 0%,
            var(--border-color-enhanced, #e2e8f0) 20%,
            var(--nisshin-red-primary, #dc2626) 50%,
            var(--border-color-enhanced, #e2e8f0) 80%,
            transparent 100%);
        border-radius: 50px;
    }

    &::after {
        content: '';
        position: absolute;
        top: -0.5px;
        left: 25%;
        right: 25%;
        height: 2px;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(220, 38, 38, 0.2) 50%,
            transparent 100%);
        border-radius: 50px;
        filter: blur(0.5px);
    }
}

/* ===== ENHANCED FORM STYLING ===== */
.field {
    margin-bottom: var(--spacing-lg, 1.5rem);

    label {
        display: block;
        margin-bottom: var(--spacing-sm, 0.5rem);
        font-weight: 600;
        color: var(--text-primary-enhanced, #1e293b);
        font-size: 0.875rem;
        letter-spacing: 0.025em;
    }

    .p-error {
        display: block;
        margin-top: var(--spacing-xs, 0.25rem);
        font-size: 0.75rem;
        color: var(--nisshin-red-primary, #dc2626);
        font-weight: 500;
    }
}

/* ===== LOADING STATES ===== */
.layout-theme-light .loading-shimmer {
    background: linear-gradient(90deg,
        #f0f0f0 25%,
        #e0e0e0 50%,
        #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

.layout-theme-dark .loading-shimmer {
    background: linear-gradient(90deg,
        #374151 25%,
        #4b5563 50%,
        #374151 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}
